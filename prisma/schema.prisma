generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model user {
  id           Int            @id @default(autoincrement())
  name         String?
  email        String?
  phone        String         @unique
  password     String?
  mpin         String?
  role         Role           @default(reporter)
  status       UserStatus     @default(inactive)
  createdAt    DateTime       @default(now())
  auditLogs    audit_log[]
  reporter     reporter?
  comments     comment[]
  articleLikes article_like[]
  commentLikes comment_like[]
  articleReads article_read[]
}

model country {
  id        Int        @id @default(autoincrement())
  name      String
  code      String?
  reporters reporter[]
  states    state[]
}

model state {
  id        Int        @id @default(autoincrement())
  name      String
  code      String?
  countryId Int
  articles  article[]
  districts district[]
  reporters reporter[]
  country   country    @relation(fields: [countryId], references: [id])
}

model district {
  id             Int            @id @default(autoincrement())
  name           String
  stateId        Int
  articles       article[]
  constituencies constituency[]
  state          state          @relation(fields: [stateId], references: [id])
  reporters      reporter[]
}

model constituency {
  id         Int        @id @default(autoincrement())
  name       String
  districtId Int
  articles   article[]
  district   district   @relation(fields: [districtId], references: [id])
  mandals    mandal[]
  reporters  reporter[]
}

model mandal {
  id             Int          @id @default(autoincrement())
  name           String
  constituencyId Int
  articles       article[]
  constituency   constituency @relation(fields: [constituencyId], references: [id])
  reporters      reporter[]
}

model reporter {
  id                Int            @id @default(autoincrement())
  userId            Int            @unique
  level             ReporterLevel
  parentId          Int?
  countryId         Int
  stateId           Int
  districtId        Int?
  constituencyId    Int?
  mandalId          Int?
  autoLive          Boolean        @default(false)
  paymentDue        Boolean        @default(true)
  profilePhotoUrl   String?
  idCardUrl         String?
  joiningDate       DateTime       @default(now())
  joiningFee        Decimal?
  monthlyFee        Decimal?
  kyc_verification  Boolean        @default(false)
  kyc_data          Json?
  isApprovedByAdmin Boolean        @default(false)
  subscribed        Boolean        @default(false)
  articles          article[]
  payments          payment[]
  constituency      constituency?  @relation(fields: [constituencyId], references: [id])
  country           country        @relation(fields: [countryId], references: [id])
  district          district?      @relation(fields: [districtId], references: [id])
  mandal            mandal?        @relation(fields: [mandalId], references: [id])
  parent            reporter?      @relation("ReporterHierarchy", fields: [parentId], references: [id])
  children          reporter[]     @relation("ReporterHierarchy")
  state             state          @relation(fields: [stateId], references: [id])
  user              user           @relation(fields: [userId], references: [id])
  subscriptions     subscription[]
}

model article {
  id              Int            @id @default(autoincrement())
  reporterId      Int
  title           String
  slug            String         @unique
  content         String
  imageUrl        String?
  languageCode    String?        @db.VarChar(10)
  language        languages?     @relation(fields: [languageCode], references: [code])
  stateId         Int?
  districtId      Int?
  constituencyId  Int?
  mandalId        Int?
  villageName     String?
  latitude        Decimal?
  longitude       Decimal?
  isLive          Boolean        @default(false)
  isBreaking      Boolean        @default(false)
  createdAt       DateTime       @default(now())
  metaTitle       String
  metaDescription String
  keywords        String[]
  constituency    constituency?  @relation(fields: [constituencyId], references: [id])
  district        district?      @relation(fields: [districtId], references: [id])
  mandal          mandal?        @relation(fields: [mandalId], references: [id])
  reporter        reporter       @relation(fields: [reporterId], references: [id])
  state           state?         @relation(fields: [stateId], references: [id])
  comments        comment[]
  articleLikes    article_like[]
  articleReads    article_read[]
  categoryId      Int?
  category        category?      @relation(fields: [categoryId], references: [id])
}

model comment {
  id           Int            @id @default(autoincrement())
  content      String
  articleId    Int
  userId       Int
  createdAt    DateTime       @default(now())
  article      article        @relation(fields: [articleId], references: [id])
  user         user           @relation(fields: [userId], references: [id])
  commentLikes comment_like[]
  parentId     Int? // for replies
  parent       comment?       @relation("CommentReplies", fields: [parentId], references: [id])
  replies      comment[]      @relation("CommentReplies")
}

model category {
  id       Int        @id @default(autoincrement())
  name     String
  parentId Int?
  parent   category?  @relation("ParentCategory", fields: [parentId], references: [id])
  children category[] @relation("ParentCategory")
  articles article[]
}

model payment {
  id               Int           @id @default(autoincrement())
  reporterId       Int
  amount           Decimal?
  status           PaymentStatus @default(unpaid)
  paidOn           DateTime?
  monthFor         DateTime?
  paymentMethod    String?
  paymentReference String?
  reporter         reporter      @relation(fields: [reporterId], references: [id])
}

model subscription {
  id                    Int                @id @default(autoincrement())
  reporterId            Int
  planName              String
  amount                Decimal?
  startDate             DateTime
  endDate               DateTime
  status                SubscriptionStatus @default(active)
  phonepeSubscriptionId String?
  nextPaymentDate       DateTime?
  reporter              reporter           @relation(fields: [reporterId], references: [id])
}

model audit_log {
  id        Int      @id @default(autoincrement())
  userId    Int
  action    String
  details   String?
  timestamp DateTime @default(now())
  user      user     @relation(fields: [userId], references: [id])
}

model otp_log {
  id        Int      @id @default(autoincrement())
  phone     String
  otp       String
  expiresAt DateTime
  createdAt DateTime @default(now())
}

model languages {
  id               Int       @id @default(autoincrement())
  code             String    @unique @db.VarChar(10)
  name             String    @db.VarChar(50)
  name_in_language String?   @db.VarChar(100)
  language_color   String?   @db.VarChar(20)
  articles         article[]
}

model article_like {
  id        Int      @id @default(autoincrement())
  userId    Int
  articleId Int
  isLike    Boolean
  createdAt DateTime @default(now())
  user      user     @relation(fields: [userId], references: [id])
  article   article  @relation(fields: [articleId], references: [id])

  @@unique([userId, articleId])
}

model comment_like {
  id        Int      @id @default(autoincrement())
  userId    Int
  commentId Int
  createdAt DateTime @default(now())
  user      user     @relation(fields: [userId], references: [id])
  comment   comment  @relation(fields: [commentId], references: [id])

  @@unique([userId, commentId])
}

model device {
  id           Int            @id @default(autoincrement())
  deviceId     String         @unique
  fcmToken     String?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  articleReads article_read[]
}

model article_read {
  id        Int      @id @default(autoincrement())
  deviceId  Int?
  userId    Int?
  articleId Int
  read      Boolean  @default(false)
  updatedAt DateTime @updatedAt
  device    device?  @relation(fields: [deviceId], references: [id])
  user      user?    @relation(fields: [userId], references: [id])
  article   article  @relation(fields: [articleId], references: [id])

  @@unique([deviceId, userId, articleId])
}

enum Role {
  admin
  desk
  reporter
  citizen
}

enum UserStatus {
  active
  inactive
}

enum ReporterLevel {
  BUREAU_REPORTER
  STAFF_REPORTER
  CRIME_REPORTER
  RC_INCHARGE
  REPORTER
}

enum PaymentStatus {
  paid
  unpaid
  failed
}

enum SubscriptionStatus {
  active
  expired
  paused
}

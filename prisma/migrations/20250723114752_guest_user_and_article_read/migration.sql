-- CreateTable
CREATE TABLE "device" (
    "id" SERIAL NOT NULL,
    "deviceId" TEXT NOT NULL,
    "fcmToken" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "device_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "article_read" (
    "id" SERIAL NOT NULL,
    "deviceId" INTEGER,
    "userId" INTEGER,
    "articleId" INTEGER NOT NULL,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "article_read_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "device_deviceId_key" ON "device"("deviceId");

-- CreateIndex
CREATE UNIQUE INDEX "article_read_deviceId_userId_articleId_key" ON "article_read"("deviceId", "userId", "articleId");

-- AddForeignKey
ALTER TABLE "article_read" ADD CONSTRAINT "article_read_deviceId_fkey" FOREIGN KEY ("deviceId") REFERENCES "device"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "article_read" ADD CONSTRAINT "article_read_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "article_read" ADD CONSTRAINT "article_read_articleId_fkey" FOREIGN KEY ("articleId") REFERENCES "article"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

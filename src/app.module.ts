import { Modu<PERSON> } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { ConfigModule } from "@nestjs/config";
import { HttpModule } from "@nestjs/axios";
import { AuthModule } from "./auth/auth.module";
import { PrismaModule } from "./prisma/prisma.module";
import { UtilityModule } from "./utility/utility.module";
import { ReportersModule } from "./reporters/reporters.module";
import { ArticlesModule } from "./articles/articles.module";
import { PaymentsModule } from "./payments/payments.module";
import { LocationsModule } from "./locations/locations.module";
import { CommonModule } from "./common/common.module";
import { CitizenModule } from "./citizen/citizen.module";
@Module({
  imports: [
    HttpModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
    }),
    PrismaModule,
    AuthModule,
    UtilityModule,
    ReportersModule,
    ArticlesModule,
    PaymentsModule,
    LocationsModule,
    CommonModule,
    CitizenModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

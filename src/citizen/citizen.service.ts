import { Injectable } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CitizenRegisterDto } from "./dto/citizen-register.dto";
import { CitizenLoginDto } from "./dto/citizen-login.dto";
import { CitizenRequestOtpDto } from "./dto/citizen-request-otp.dto";
import { CitizenVerifyOtpDto } from "./dto/citizen-verify-otp.dto";
import { Role, UserStatus } from "@prisma/client";
import * as bcrypt from "bcrypt";
import { WhatsappService } from "../utility/whatsapp.service";
import { JwtService } from "../auth/jwt.service";

@Injectable()
export class CitizenService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly whatsappService: WhatsappService,
    private readonly jwtService: JwtService
  ) {}

  async register(dto: CitizenRegisterDto) {
    const existingUser = await this.prisma.user.findFirst({
      where: { phone: dto.phone },
    });
    if (existingUser) {
      return {
        success: false,
        statusCode: 409,
        message: "User already exists with this phone number",
        data: {},
      };
    }
    const user = await this.prisma.user.create({
      data: {
        name: dto.name,
        phone: dto.phone,
        email: dto.email,
        role: Role.citizen,
        status: UserStatus.active,
      },
    });
    return {
      success: true,
      statusCode: 201,
      message: "Citizen registered successfully",
      data: user,
    };
  }

  async login(dto: CitizenLoginDto) {
    const user = await this.prisma.user.findUnique({
      where: { phone: dto.phone },
    });
    if (!user) {
      return {
        success: false,
        statusCode: 401,
        message: "Invalid credentials",
        data: {},
      };
    }
    if (!user.mpin) {
      return {
        success: false,
        statusCode: 401,
        message: "MPIN not set",
        data: {},
      };
    }
    const isValidMpin = await bcrypt.compare(dto.mpin, user.mpin);
    if (!isValidMpin) {
      return {
        success: false,
        statusCode: 401,
        message: "Invalid MPIN",
        data: {},
      };
    }
    // TODO: Generate JWT token for citizen
    return {
      success: true,
      statusCode: 200,
      message: "Login successful",
      data: user,
    };
  }

  async requestOtp(dto: CitizenRequestOtpDto) {
    const user = await this.prisma.user.findFirst({
      where: { phone: dto.phone, role: Role.citizen },
    });
    if (!user) {
      return {
        success: false,
        statusCode: 404,
        message: "Citizen not found",
        data: {},
      };
    }
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    await this.prisma.otp_log.create({
      data: {
        phone: dto.phone,
        otp,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000),
      },
    });
    const sent = await this.whatsappService.sendOtp(dto.phone, otp);
    if (!sent) {
      return {
        success: false,
        statusCode: 400,
        message: "Failed to send OTP via WhatsApp",
        data: {},
      };
    }
    return {
      success: true,
      statusCode: 200,
      message: "OTP sent successfully via WhatsApp",
      data: {},
    };
  }

  async verifyOtp(dto: CitizenVerifyOtpDto) {
    const user = await this.prisma.user.findFirst({
      where: { phone: dto.phone, role: Role.citizen },
    });
    if (!user) {
      return {
        success: false,
        statusCode: 404,
        message: "Citizen not found",
        data: {},
      };
    }
    const otpRecord = await this.prisma.otp_log.findFirst({
      where: {
        phone: dto.phone,
        otp: dto.otp,
        expiresAt: { gt: new Date() },
      },
    });
    if (!otpRecord) {
      return {
        success: false,
        statusCode: 400,
        message: "Invalid or expired OTP",
        data: {},
      };
    }
    const token = await this.jwtService.generateToken({
      userId: user.id,
      phone: user.phone,
      role: user.role,
    });
    return {
      success: true,
      statusCode: 200,
      message: "Login successful",
      data: { token, user },
    };
  }
}

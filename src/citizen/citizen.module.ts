import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { PrismaModule } from "../prisma/prisma.module";
import { UtilityModule } from "../utility/utility.module";
import { AuthModule } from "../auth/auth.module";
import { CitizenService } from "./citizen.service";
import { CitizenController } from "./citizen.controller";

@Module({
  imports: [PrismaModule, UtilityModule, AuthModule],
  providers: [CitizenService],
  controllers: [CitizenController],
  exports: [CitizenService],
})
export class CitizenModule {}

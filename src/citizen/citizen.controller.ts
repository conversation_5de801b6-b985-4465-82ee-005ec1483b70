import { Controller, Post, Body, HttpCode, HttpStatus } from "@nestjs/common";
import { CitizenService } from "./citizen.service";
import { CitizenRegisterDto } from "./dto/citizen-register.dto";
import { CitizenLoginDto } from "./dto/citizen-login.dto";
import { CitizenRequestOtpDto } from "./dto/citizen-request-otp.dto";
import { CitizenVerifyOtpDto } from "./dto/citizen-verify-otp.dto";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";

@ApiTags("👤 Citizen")
@Controller("api/citizen")
export class CitizenController {
  constructor(private readonly citizenService: CitizenService) {}

  @Post("register")
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: "Register a new citizen",
    description:
      "Create a new citizen account with phone number, name, and optional email.",
  })
  @ApiBody({
    type: CitizenRegisterDto,
    description: "Citizen registration details",
    examples: {
      example1: {
        summary: "Standard Registration",
        value: {
          name: "<PERSON><PERSON>",
          phone: "**********",
          email: "<EMAIL>",
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: "Citizen registered successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        statusCode: { type: "number", example: 201 },
        message: { type: "string", example: "Citizen registered successfully" },
        data: {
          type: "object",
          properties: {
            id: { type: "number", example: 1 },
            name: { type: "string", example: "Amit Kumar" },
            phone: { type: "string", example: "**********" },
            email: { type: "string", example: "<EMAIL>" },
            status: { type: "string", example: "active" },
            role: { type: "string", example: "citizen" },
          },
        },
      },
    },
  })
  async register(@Body() dto: CitizenRegisterDto) {
    return this.citizenService.register(dto);
  }

  @Post("login")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "Citizen login",
    description: "Authenticate citizen with phone number and MPIN.",
  })
  @ApiBody({
    type: CitizenLoginDto,
    description: "Citizen login credentials",
    examples: {
      example1: {
        summary: "Standard Login",
        value: {
          phone: "**********",
          mpin: "123456",
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Login successful",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        statusCode: { type: "number", example: 200 },
        message: { type: "string", example: "Login successful" },
        data: {
          type: "object",
          properties: {
            id: { type: "number", example: 1 },
            name: { type: "string", example: "Amit Kumar" },
            phone: { type: "string", example: "**********" },
            email: { type: "string", example: "<EMAIL>" },
            role: { type: "string", example: "citizen" },
            status: { type: "string", example: "active" },
          },
        },
      },
    },
  })
  async login(@Body() dto: CitizenLoginDto) {
    return this.citizenService.login(dto);
  }

  @Post("request-otp")
  @ApiOperation({
    summary: "Request OTP for citizen login",
    description: "Send OTP to the citizen's phone via WhatsApp for login.",
  })
  @ApiBody({ type: CitizenRequestOtpDto })
  @ApiResponse({ status: 200, description: "OTP sent successfully" })
  async requestOtp(@Body() dto: CitizenRequestOtpDto) {
    return this.citizenService.requestOtp(dto);
  }

  @Post("verify-otp")
  @ApiOperation({
    summary: "Citizen login with OTP",
    description: "Verify OTP and login the citizen, returning a JWT token.",
  })
  @ApiBody({ type: CitizenVerifyOtpDto })
  @ApiResponse({
    status: 200,
    description: "Login successful, returns JWT token and user info",
  })
  async verifyOtp(@Body() dto: CitizenVerifyOtpDto) {
    return this.citizenService.verifyOtp(dto);
  }
}

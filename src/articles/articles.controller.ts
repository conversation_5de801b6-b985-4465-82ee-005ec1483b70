import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Query,
  UseGuards,
  Request,
} from "@nestjs/common";
import { ArticlesService } from "./articles.service";
import { CreateArticleDto } from "./dto/create-article.dto";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { RolesGuard } from "../auth/roles.guard";
import { Roles } from "../auth/roles.decorator";
import { Role } from "@prisma/client";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
} from "@nestjs/swagger";

@ApiTags("📰 Articles")
@Controller("api/articles")
export class ArticlesController {
  constructor(private readonly articlesService: ArticlesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth("JWT-auth")
  @ApiOperation({
    summary: "Create article",
    description:
      "Create a new article. Requires authentication and reporter profile.",
  })
  @ApiBody({
    type: CreateArticleDto,
    description: "Article creation details",
    examples: {
      example1: {
        summary: "News Article",
        value: {
          title: "Breaking News: Important Update",
          content: "This is the detailed content of the news article...",
          languageCode: "en",
          metaDescription:
            "Important news update about recent developments in the region",
          metaTitle: "Breaking News Update",
          keywords: ["politics", "breaking", "local"],
          countryId: 1,
          stateId: 1,
          districtId: 1,
          constituencyId: 1,
          mandalId: 1,
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: "Article created successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        statusCode: { type: "number", example: 201 },
        message: { type: "string", example: "Article created successfully" },
        data: {
          type: "object",
          properties: {
            id: { type: "number", example: 1 },
            title: {
              type: "string",
              example: "Breaking News: Important Update",
            },
            slug: { type: "string", example: "breaking-news-important-update" },
            languageCode: { type: "string", example: "en" },
            isLive: { type: "boolean", example: false },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: "Invalid or missing JWT token",
  })
  async create(
    @Request() req: any,
    @Body() createArticleDto: CreateArticleDto
  ) {
    return this.articlesService.create(req.user.userId, createArticleDto);
  }

  @Get()
  @ApiOperation({
    summary: "Get all articles",
    description:
      "Get all published articles with optional filters for language and state. Supports cursor-based pagination.",
  })
  @ApiQuery({
    name: "language",
    required: false,
    description: "Filter by language code (e.g., en, hi, te)",
    example: "en",
  })
  @ApiQuery({
    name: "state",
    required: false,
    description: "Filter by state name",
    example: "telangana",
  })
  @ApiQuery({
    name: "after",
    required: false,
    description: "Cursor for pagination (last article id)",
    example: 100,
  })
  @ApiQuery({
    name: "limit",
    required: false,
    description: "Number of articles to fetch",
    example: 10,
  })
  @ApiQuery({
    name: "readStatus",
    required: false,
    description: "Filter by read/unread status (read|unread)",
    example: "read",
  })
  @ApiQuery({
    name: "deviceId",
    required: false,
    description: "Device ID for read/unread filter",
    example: "abc123-device-id",
  })
  @ApiQuery({
    name: "userId",
    required: false,
    description: "User ID for read/unread filter",
    example: 1,
  })
  @ApiQuery({
    name: "categoryId",
    required: false,
    description: "Filter by category ID",
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: "Articles retrieved successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        statusCode: { type: "number", example: 200 },
        message: { type: "string", example: "Articles retrieved successfully" },
        data: {
          type: "object",
          properties: {
            articles: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "number", example: 1 },
                  title: { type: "string", example: "Breaking News" },
                  content: { type: "string", example: "Article content..." },
                  languageCode: { type: "string", example: "en" },
                  isLive: { type: "boolean", example: true },
                  createdAt: {
                    type: "string",
                    example: "2024-01-01T00:00:00Z",
                  },
                  likeCount: { type: "number", example: 15 },
                  dislikeCount: { type: "number", example: 2 },
                  commentCount: { type: "number", example: 8 },
                },
              },
            },
            nextCursor: { type: "number", example: 110 },
          },
        },
      },
    },
  })
  async findAll(
    @Query("language") language?: string,
    @Query("state") state?: string,
    @Query("after") after?: string,
    @Query("limit") limit?: string,
    @Query("categoryId") categoryId?: string,
    @Query("readStatus") readStatus?: string,
    @Query("deviceId") deviceId?: string,
    @Query("userId") userId?: string
  ) {
    return this.articlesService.findAll({
      language,
      state,
      after,
      limit,
      categoryId,
      readStatus,
      deviceId,
      userId,
    });
  }

  @Get("/categories")
  @ApiOperation({
    summary: "Get all categories",
    description: "Returns a list of all news categories.",
  })
  @ApiResponse({
    status: 200,
    description: "Categories retrieved successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        statusCode: { type: "number" },
        message: { type: "string" },
        data: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "number" },
              name: { type: "string" },
              parentId: { type: "number", nullable: true },
            },
          },
        },
      },
    },
  })
  async getAllCategories() {
    return this.articlesService.getAllCategories();
  }

  @Put(":id/status")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.desk, Role.admin, Role.reporter)
  @ApiBearerAuth("JWT-auth")
  @ApiOperation({
    summary: "Update article status",
    description:
      "Update the live status of an article. Only authorized users can publish/unpublish articles.",
  })
  @ApiParam({
    name: "id",
    description: "Article ID",
    example: 1,
    type: "number",
  })
  @ApiBody({
    description: "Article status update",
    schema: {
      type: "object",
      properties: {
        isLive: {
          type: "boolean",
          example: true,
          description: "Whether the article should be live/published",
        },
      },
      required: ["isLive"],
    },
    examples: {
      publish: {
        summary: "Publish Article",
        value: {
          isLive: true,
        },
      },
      unpublish: {
        summary: "Unpublish Article",
        value: {
          isLive: false,
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Article status updated successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        statusCode: { type: "number", example: 200 },
        message: {
          type: "string",
          example: "Article status updated successfully",
        },
        data: {
          type: "object",
          properties: {
            id: { type: "number", example: 1 },
            isLive: { type: "boolean", example: true },
            updatedAt: { type: "string", example: "2024-01-01T00:00:00Z" },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: "Invalid or missing JWT token",
  })
  @ApiNotFoundResponse({
    description: "Article not found",
  })
  async updateStatus(
    @Param("id") id: string,
    @Request() req: any,
    @Body("isLive") isLive: boolean
  ) {
    return this.articlesService.updateStatus(+id, req.user.userId, isLive);
  }

  @Post("read-status")
  @ApiOperation({
    summary: "Update read/unread status for articles",
    description:
      "Batch update read/unread status for articles for a device or user.",
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        deviceId: { type: "string", nullable: true },
        userId: { type: "number", nullable: true },
        articles: {
          type: "array",
          items: {
            type: "object",
            properties: {
              articleId: { type: "number" },
              read: { type: "boolean" },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Read/unread status updated successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        statusCode: { type: "number" },
        message: { type: "string" },
        data: { type: "object" },
      },
    },
  })
  async updateReadStatus(
    @Body()
    body: {
      deviceId?: string;
      userId?: number;
      articles: { articleId: number; read: boolean }[];
    }
  ) {
    // Implement logic in ArticlesService (to be added)
    return this.articlesService.updateReadStatus(body);
  }

  @Post(":id/like/:like")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth("JWT-auth")
  async likeArticle(
    @Param("id") id: string,
    @Request() req: any,
    @Param("like") like: boolean
  ) {
    return this.articlesService.likeArticle(+id, req.user.userId, like);
  }

  @Post(":id/comments")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth("JWT-auth")
  async addComment(
    @Param("id") id: string,
    @Request() req: any,
    @Body("content") content: string
  ) {
    return this.articlesService.addComment(+id, req.user.userId, content);
  }

  @Post(":articleId/comments/:commentId/reply")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth("JWT-auth")
  async replyToComment(
    @Param("articleId") articleId: string,
    @Param("commentId") commentId: string,
    @Request() req: any,
    @Body("content") content: string
  ) {
    return this.articlesService.replyToComment(
      +articleId,
      +commentId,
      req.user.userId,
      content
    );
  }

  @Post(":articleId/comments/:commentId/like")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth("JWT-auth")
  async likeComment(
    @Param("commentId") commentId: string,
    @Request() req: any
  ) {
    return this.articlesService.likeComment(+commentId, req.user.userId);
  }

  @Get(":id/public")
  async getPublicArticle(@Param("id") id: string) {
    return this.articlesService.getPublicArticle(+id);
  }
}

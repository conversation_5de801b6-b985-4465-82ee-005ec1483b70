import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { transliterate } from "transliteration";
import { CreateArticleDto, LanguageCode } from "./dto/create-article.dto";
import { Role, Prisma } from "@prisma/client";
import slugify from "slugify";
import { IResponse } from "../types/index";
import { ResponseUtil } from "../common/utils/response.util";

@Injectable()
export class ArticlesService {
  private readonly logger = new Logger(ArticlesService.name);
  constructor(private prisma: PrismaService) {}

  private async translateToEnglish(
    text: string,
    languageCode: LanguageCode
  ): Promise<string> {
    if (languageCode === LanguageCode.EN) return text;

    // Replace this with real API integration
    // E.g., Google Translate API call here
    const translated = transliterate(text); // <-- implement this
    this.logger.debug(`Translated "${text}" to "${translated}"`);
    return translated;
  }

  private async generateUniqueSlug(
    originalText: string,
    languageCode: LanguageCode
  ): Promise<string> {
    // Translate the text to English
    const translatedText = await this.translateToEnglish(
      originalText,
      languageCode
    );

    // Create a slug from translated English text
    let baseSlug = slugify(translatedText, {
      lower: true,
      strict: true,
      trim: true,
    });

    let slug = baseSlug;
    let counter = 0;

    while (true) {
      const existingArticle = await this.prisma.article.findFirst({
        where: { slug },
      });

      if (!existingArticle) break;
      counter++;
      slug = `${baseSlug}-${counter}`;
    }

    return slug;
  }

  async create(
    userId: number,
    createArticleDto: CreateArticleDto
  ): Promise<IResponse> {
    try {
      const user = await this.prisma.user.findFirst({
        where: { id: userId },
        include: {
          reporter: true,
        },
      });

      if (!user) {
        return ResponseUtil.error("User not found", 404);
      }

      // Debug log the incoming data
      this.logger.debug(
        `Creating article with language: ${createArticleDto.languageCode}, title: ${createArticleDto.title}`
      );

      // Generate slug using the new method
      const slug = await this.generateUniqueSlug(
        createArticleDto.title,
        createArticleDto.languageCode
      );

      // Debug log the generated slug
      this.logger.debug(`Generated slug: ${slug}`);

      const locationData: any = {};
      if (createArticleDto.latitude && createArticleDto.longitude) {
        locationData.latitude = createArticleDto.latitude;
        locationData.longitude = createArticleDto.longitude;
      }
      locationData.stateId = createArticleDto.stateId;
      locationData.districtId = createArticleDto.districtId;
      locationData.constituencyId = createArticleDto.constituencyId;
      locationData.mandalId = createArticleDto.mandalId;
      locationData.villageName = createArticleDto.villageName;

      // Add categoryId if provided
      if (createArticleDto.categoryId) {
        locationData.categoryId = createArticleDto.categoryId;
      }

      const article = await this.prisma.article.create({
        data: {
          title: createArticleDto.title,
          content: createArticleDto.content,
          imageUrl: createArticleDto.imageUrl,
          languageCode: createArticleDto.languageCode,
          metaTitle: createArticleDto.metaTitle,
          metaDescription: createArticleDto.metaDescription,
          keywords: createArticleDto.keywords,
          slug,
          reporterId: user.reporter?.id,
          isLive: user.reporter?.autoLive || false,
          isBreaking: createArticleDto?.isBreaking,
          ...locationData,
        },
        include: {
          reporter: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          state: true,
          district: true,
          constituency: true,
          mandal: true,
          category: true,
        },
      });

      return ResponseUtil.success(article, "Article created successfully", 201);
    } catch (error) {
      this.logger.error("Failed to create article", error.stack);
      return ResponseUtil.error("Failed to create article", 500);
    }
  }

  async findAll(params: {
    language?: string;
    state?: string;
    after?: string;
    limit?: string;
    readStatus?: string;
    deviceId?: string;
    userId?: string;
    categoryId?: string;
  }): Promise<IResponse> {
    try {
      const {
        language,
        state,
        after,
        limit,
        readStatus,
        deviceId,
        userId,
        categoryId,
      } = params;
      const take = limit ? parseInt(limit, 10) : 10;
      const whereClause: Prisma.articleWhereInput = {
        isLive: true,
        ...(language ? { languageCode: language } : {}),
        ...(state ? { state: { name: state } } : {}),
        ...(after ? { id: { gt: Number(after) } } : {}),
        ...(categoryId ? { categoryId: Number(categoryId) } : {}),
      };
      console.log(whereClause, "whereClause", categoryId, params);
      // Read/unread filter
      let deviceDbId: number | null = null;
      if (deviceId) {
        const device = await this.prisma.device.findUnique({
          where: { deviceId },
        });
        if (device) {
          deviceDbId = device.id;
        }
      }
      if (readStatus && (deviceDbId || userId)) {
        whereClause.articleReads = {
          some: {
            ...(deviceDbId ? { deviceId: deviceDbId } : {}),
            ...(userId ? { userId: Number(userId) } : {}),
            read: readStatus === "read",
          },
        };
      }
      const articles = await this.prisma.article.findMany({
        where: whereClause,
        include: {
          reporter: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          state: true,
          district: true,
          constituency: true,
          mandal: true,
          articleReads: true,
        },
        orderBy: {
          id: "asc",
        },
        take,
      });
      const nextCursor =
        articles.length === take ? articles[articles.length - 1].id : null;
      return ResponseUtil.success(
        { articles, nextCursor },
        "Articles retrieved successfully"
      );
    } catch (error) {
      this.logger.error("Failed to retrieve articles", error.stack);
      return ResponseUtil.error("Failed to retrieve articles", 500);
    }
  }

  async updateStatus(
    id: number,
    userId: number,
    isLive: boolean
  ): Promise<IResponse> {
    try {
      // First check if the article exists
      const existingArticle = await this.prisma.article.findUnique({
        where: { id },
      });
      const currentUser = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!existingArticle) {
        return ResponseUtil.error("Article not found", 404);
      }

      const user = await this.prisma.reporter.findFirst({
        where: { id: existingArticle.reporterId },
      });

      if (!user) {
        return ResponseUtil.error("User not found", 404);
      }

      if (
        userId !== user?.parentId &&
        currentUser.role !== Role.desk &&
        currentUser.role !== Role.admin
      ) {
        return ResponseUtil.error(
          "Only desk, admin users and parent user can update article status",
          404
        );
      }

      const article = await this.prisma.article.update({
        where: { id },
        data: { isLive },
        include: {
          reporter: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });

      return ResponseUtil.success(
        article,
        `Article ${isLive ? "published" : "unpublished"} successfully`
      );
    } catch (error) {
      this.logger.error(
        `Failed to update article status for article ${id}`,
        error.stack
      );
      return ResponseUtil.error("Failed to update article status", 500);
    }
  }

  async addComment(
    id: number,
    userId: number,
    content: string
  ): Promise<IResponse> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return ResponseUtil.error("User not found", 404);
      }

      const comment = await this.prisma.comment.create({
        data: {
          content,
          articleId: id,
          userId,
        },
        include: {
          user: {
            select: {
              name: true,
            },
          },
        },
      });

      return ResponseUtil.success(comment, "Comment added successfully", 201);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case "P2025":
            return ResponseUtil.error("Article not found", 404);
          case "P2003":
            return ResponseUtil.error("Invalid article reference", 400);
          default:
            return ResponseUtil.error("Failed to add comment", 400);
        }
      }
      this.logger.error("Failed to add comment", error.stack);
      return ResponseUtil.error("Failed to add comment", 500);
    }
  }

  async likeArticle(
    articleId: number,
    userId: number,
    like: boolean
  ): Promise<IResponse> {
    try {
      await this.prisma.article_like.upsert({
        where: { userId_articleId: { userId, articleId } },
        update: { isLike: like },
        create: { userId, articleId, isLike: like },
      });
      return ResponseUtil.success(
        {},
        `Article ${like ? "liked" : "disliked"} successfully`
      );
    } catch (error) {
      this.logger.error("Failed to like/dislike article", error.stack);
      return ResponseUtil.error("Failed to like/dislike article", 500);
    }
  }

  async replyToComment(
    articleId: number,
    commentId: number,
    userId: number,
    content: string
  ): Promise<IResponse> {
    try {
      const user = await this.prisma.user.findUnique({ where: { id: userId } });
      if (!user) {
        return ResponseUtil.error("User not found", 404);
      }
      const reply = await this.prisma.comment.create({
        data: {
          content,
          articleId,
          userId,
          parentId: commentId,
        },
        include: {
          user: { select: { name: true } },
        },
      });
      return ResponseUtil.success(reply, "Reply added successfully", 201);
    } catch (error) {
      this.logger.error("Failed to reply to comment", error.stack);
      return ResponseUtil.error("Failed to reply to comment", 500);
    }
  }

  async likeComment(commentId: number, userId: number): Promise<IResponse> {
    try {
      await this.prisma.comment_like.upsert({
        where: { userId_commentId: { userId, commentId } },
        update: {},
        create: { userId, commentId },
      });
      return ResponseUtil.success({}, "Comment liked successfully");
    } catch (error) {
      this.logger.error("Failed to like comment", error.stack);
      return ResponseUtil.error("Failed to like comment", 500);
    }
  }

  async getPublicArticle(articleId: number): Promise<IResponse> {
    try {
      const article = await this.prisma.article.findUnique({
        where: { id: articleId },
        include: {
          reporter: { include: { user: { select: { name: true } } } },
        },
      });
      if (!article) {
        return ResponseUtil.error("Article not found", 404);
      }
      const [likeCount, dislikeCount, commentCount] = await Promise.all([
        this.prisma.article_like.count({ where: { articleId, isLike: true } }),
        this.prisma.article_like.count({ where: { articleId, isLike: false } }),
        this.prisma.comment.count({ where: { articleId } }),
      ]);
      return ResponseUtil.success(
        {
          ...article,
          likeCount,
          dislikeCount,
          commentCount,
        },
        "Article details fetched successfully"
      );
    } catch (error) {
      this.logger.error("Failed to fetch public article details", error.stack);
      return ResponseUtil.error("Failed to fetch article details", 500);
    }
  }

  async updateReadStatus(body: {
    deviceId?: string;
    userId?: number;
    articles: { articleId: number; read: boolean }[];
  }): Promise<IResponse> {
    try {
      const { deviceId, userId, articles } = body;
      if (!deviceId && !userId) {
        return ResponseUtil.error("Either deviceId or userId is required", 400);
      }
      // Find device DB id if deviceId is provided as string
      let deviceDbId: number | null = null;
      if (deviceId) {
        const device = await this.prisma.device.findUnique({
          where: { deviceId },
        });
        if (!device) {
          return ResponseUtil.error("Device not found", 404);
        }
        deviceDbId = device.id;
      }
      for (const { articleId, read } of articles) {
        await this.prisma.article_read.upsert({
          where: {
            deviceId_userId_articleId: {
              deviceId: deviceDbId,
              userId: userId ?? null,
              articleId,
            },
          },
          update: { read },
          create: {
            deviceId: deviceDbId,
            userId: userId ?? null,
            articleId,
            read,
          },
        });
      }
      return ResponseUtil.success(
        {},
        "Read/unread status updated successfully"
      );
    } catch (error) {
      this.logger.error("Failed to update read/unread status", error.stack);
      return ResponseUtil.error("Failed to update read/unread status", 500);
    }
  }

  async getAllCategories(): Promise<IResponse> {
    try {
      const categories = await this.prisma.category.findMany();
      return ResponseUtil.success(
        categories,
        "Categories retrieved successfully"
      );
    } catch (error) {
      this.logger.error("Failed to retrieve categories", error.stack);
      return ResponseUtil.error("Failed to retrieve categories", 500);
    }
  }
}

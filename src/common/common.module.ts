import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { PrismaModule } from "../prisma/prisma.module";
import { LanguageService } from "./services/language.service";
import { LanguageController } from "./controllers/language.controller";
import { S3Service } from "./services/s3.service";
import { UploadController } from "./controllers/upload.controller";
import { <PERSON>ce<PERSON>ontroller } from "./controllers/device.controller";
import { DeviceService } from "./services/device.service";

@Module({
  imports: [PrismaModule],
  providers: [LanguageService, S3Service, DeviceService],
  controllers: [LanguageController, UploadController, DeviceController],
  exports: [LanguageService, S3Service, DeviceService],
})
export class CommonModule {}

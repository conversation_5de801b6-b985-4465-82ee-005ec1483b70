import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import {
  ApiConsumes,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { S3Service } from "../services/s3.service";

@ApiTags("🗂️ Common")
@Controller("api/common")
export class UploadController {
  constructor(private readonly s3Service: S3Service) {}

  @Post("upload-image")
  @ApiOperation({
    summary: "Upload an image to S3",
    description:
      "Uploads a single image file to S3 and returns the public URL.",
  })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: "Image uploaded successfully",
    schema: {
      type: "object",
      properties: {
        url: {
          type: "string",
          example:
            "https://vedic-erp.s3.ap-south-1.amazonaws.com/uploads/uuid.jpg",
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor("file"))
  async uploadImage(@UploadedFile() file: Express.Multer.File) {
    const url = await this.s3Service.uploadImage(file);
    return { url };
  }
}

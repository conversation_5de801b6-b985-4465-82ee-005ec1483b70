import { Controller, Get } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { LanguageService } from "../services/language.service";
import { ResponseUtil } from "../utils/response.util";

@ApiTags("🌐 Languages")
@Controller("api/languages")
export class LanguageController {
  constructor(private readonly languageService: LanguageService) {}

  @Get()
  @ApiOperation({
    summary: "Get supported languages",
    description: "Returns a list of supported languages.",
  })
  @ApiResponse({
    status: 200,
    description: "Languages retrieved successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        statusCode: { type: "number", example: 200 },
        message: {
          type: "string",
          example: "Languages retrieved successfully",
        },
        data: {
          type: "array",
          items: {
            type: "object",
            properties: {
              code: { type: "string", example: "en" },
              name: { type: "string", example: "EN" },
            },
          },
        },
      },
    },
  })
  async getLanguages() {
    const languages = await this.languageService.getLanguages();
    return ResponseUtil.success(languages, "Languages retrieved successfully");
  }
}

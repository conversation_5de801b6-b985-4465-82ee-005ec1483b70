import { <PERSON>, Post, Body } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiBody, ApiResponse } from "@nestjs/swagger";
import { DeviceService } from "../services/device.service";

@ApiTags("Device")
@Controller("api/device")
export class DeviceController {
  constructor(private readonly deviceService: DeviceService) {}

  @Post("fcm-token")
  @ApiOperation({ summary: "Store or update FCM token for a device" })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        deviceId: { type: "string", example: "abc123-device-id" },
        fcmToken: { type: "string", example: "fcm-token-xyz" },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "FCM token stored/updated successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        statusCode: { type: "number" },
        message: { type: "string" },
        data: { type: "object" },
      },
    },
  })
  async storeFcmToken(@Body() body: { deviceId: string; fcmToken: string }) {
    return this.deviceService.storeFcmToken(body.deviceId, body.fcmToken);
  }
}

import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";

@Injectable()
export class LanguageService {
  constructor(private readonly prisma: PrismaService) {}

  async getLanguages() {
    return this.prisma.languages.findMany({
      select: {
        code: true,
        name: true,
        name_in_language: true,
        language_color: true,
      },
      orderBy: { name: "asc" },
    });
  }
}

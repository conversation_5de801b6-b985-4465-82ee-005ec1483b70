import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";
import { IResponse } from "../../types";
import { ResponseUtil } from "../utils/response.util";

@Injectable()
export class DeviceService {
  private readonly logger = new Logger(DeviceService.name);
  constructor(private prisma: PrismaService) {}

  async storeFcmToken(deviceId: string, fcmToken: string): Promise<IResponse> {
    try {
      let device = await this.prisma.device.findUnique({ where: { deviceId } });
      if (!device) {
        device = await this.prisma.device.create({
          data: { deviceId, fcmToken },
        });
      } else {
        device = await this.prisma.device.update({
          where: { deviceId },
          data: { fcmToken },
        });
      }
      return ResponseUtil.success({}, "FCM token stored/updated successfully");
    } catch (error) {
      this.logger.error("Failed to store/update FCM token", error.stack);
      return ResponseUtil.error("Failed to store/update FCM token", 500);
    }
  }
}

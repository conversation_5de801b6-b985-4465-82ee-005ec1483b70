import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { PrismaModule } from "../prisma/prisma.module";
import { RazorpayService } from "./services/razorpay.service";
import { Ra<PERSON>payController } from "./controllers/razorpay.controller";

@Module({
  imports: [ConfigModule, PrismaModule],
  controllers: [RazorpayController],
  providers: [RazorpayService],
  exports: [RazorpayService],
})
export class PaymentsModule {}

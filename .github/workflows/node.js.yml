name: Node.js CI/CD

on:
  push:
    branches: [ "hiral" ]
  pull_request:
    branches: [ "hiral" ]

jobs:
  build:
    runs-on: self-hosted

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build TypeScript
        run: npm run build

      - name: Create .env file
        run: |
          echo "${{ secrets.REDALERT_ENV_DEV }}" > .env
        shell: bash

      - name: Run Prisma Migrations
        run: npx prisma migrate deploy

      - name: Restart PM2 process
        run: |
          pm2 delete redalert-api || true
          pm2 start dist/src/main.js --name redalert-api
          pm2 save

{"name": "redalert-", "version": "1.0.0", "description": "RedAlert News APP", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/src/main.js", "start:dev": "nodemon --exec ts-node src/main.ts", "build": "tsc", "start:prod": "npm run build && pm2 restart RedAlert || pm2 start dist/main.js --name RedAlert", "dev": "ts-node src/main.ts"}, "repository": {"type": "git", "url": "git+https://github.com/atshybrid/RedAlert-.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/atshybrid/RedAlert-/issues"}, "homepage": "https://github.com/atshybrid/RedAlert-#readme", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/multer": "^2.0.0", "@types/passport-jwt": "^4.0.1", "nodemon": "^3.1.10", "prisma": "6.8.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "uuid": "^11.1.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.846.0", "@aws-sdk/s3-request-presigner": "^3.846.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.9.0", "@types/jsonwebtoken": "^9.0.9", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "razorpay": "^2.9.6", "reflect-metadata": "^0.2.2", "slugify": "^1.6.6", "swagger-ui-express": "^5.0.1", "transliteration": "^2.3.5"}}